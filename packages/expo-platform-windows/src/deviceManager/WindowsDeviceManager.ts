/**
 * Windows Device Manager Implementation
 *
 * Provides device management capabilities for Windows platform including:
 * - Local desktop development
 * - Remote device deployment via WinAppDeployTool
 * - Windows emulator support
 * - UWP and WinAppSDK app installation
 *
 * This implementation reuses react-native-windows infrastructure for maximum compatibility.
 */

import { DeviceManager } from '@expo/cli/build/src/start/platforms/DeviceManager';
import { BaseResolveDeviceProps } from '@expo/cli/build/src/start/platforms/PlatformManager';
import WinAppDeployTool from '@react-native-windows/cli/lib-commonjs/utils/winappdeploytool';
import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

// Import Expo CLI base classes for proper integration

// Simple device interface for Windows platform
export interface Device {
  id: string;
  name: string;
  isAvailable: boolean;
}

export interface DeviceSelectionOptions {
  deviceId?: string;
}

export interface WindowsDevice extends Device {
  type: 'desktop' | 'remote' | 'emulator' | 'hololens';
  architecture?: 'x86' | 'x64' | 'ARM64';
  osVersion?: string;
  deploymentType?: 'uwp' | 'winappsdk' | 'desktop';
  // Internal field to store react-native-windows device info for deployment
  _deviceInfo?: any;
}

/**
 * Windows Device Manager that handles all Windows device operations
 * including local desktop, remote devices, and emulators.
 *
 * Extends Expo CLI's DeviceManager base class for proper integration.
 * Reuses react-native-windows infrastructure for device operations.
 */
export class WindowsDeviceManager extends DeviceManager<WindowsDevice> {
  public device: WindowsDevice;
  private cachedDevices: WindowsDevice[] = [];
  private lastDeviceRefresh = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds

  constructor(device: WindowsDevice) {
    super(device);
    this.device = device;
  }

  /**
   * Static method to resolve a Windows device (required by External Platform System)
   */
  static async resolveAsync({
    device,
    shouldPrompt,
  }: BaseResolveDeviceProps<WindowsDevice> = {}): Promise<WindowsDeviceManager> {
    // Create a temporary instance to get available devices
    const tempDevice: WindowsDevice = {
      id: 'temp',
      name: 'Temp',
      type: 'desktop',
      isAvailable: true,
    };
    const tempManager = new WindowsDeviceManager(tempDevice);

    if (device) {
      return new WindowsDeviceManager(device);
    }

    const devices = await tempManager.getDevicesAsync();

    if (shouldPrompt && devices.length > 1) {
      // TODO: Implement device selection prompt
      // For now, just use the first available device
    }

    const selectedDevice = devices[0];
    if (!selectedDevice) {
      throw new Error('No Windows devices available');
    }

    return new WindowsDeviceManager(selectedDevice);
  }

  // Required abstract method implementations from DeviceManager base class
  get name(): string {
    return this.device.name;
  }

  get identifier(): string {
    return this.device.id;
  }

  async startAsync(): Promise<WindowsDevice> {
    // For Windows devices, we don't need to "start" them like simulators
    // Just return the device as it's already available
    return this.device;
  }

  async getAppVersionAsync(
    applicationId: string,
    options?: { containerPath?: string }
  ): Promise<string | null> {
    // TODO: Implement Windows app version detection
    // This would query the Windows registry or app manifest
    return null;
  }

  async uninstallAppAsync(applicationId: string): Promise<void> {
    // TODO: Implement Windows app uninstallation
    // This would use PowerShell or Windows Package Manager
    throw new Error('Windows app uninstallation not yet implemented');
  }

  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(
    applicationId: string
  ): Promise<boolean | string> {
    // This method is iOS-specific, for Windows we just return boolean
    return this.isAppInstalledAsync(this.device, applicationId);
  }

  async activateWindowAsync(): Promise<void> {
    // For Windows desktop, bring the app window to foreground
    // TODO: Implement window activation using Windows APIs
  }

  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {
    // Windows doesn't use Expo Go, return false
    return false;
  }

  getExpoGoAppId(): string {
    // Windows doesn't use Expo Go
    return '';
  }

  /**
   * Get all available Windows devices including desktop, remote devices, and emulators
   */
  async getDevicesAsync(): Promise<WindowsDevice[]> {
    const now = Date.now();
    if (now - this.lastDeviceRefresh < this.CACHE_DURATION && this.cachedDevices.length > 0) {
      return this.cachedDevices;
    }

    const devices: WindowsDevice[] = [];

    // Always include local desktop as primary development target
    devices.push({
      id: 'desktop',
      name: 'Local Desktop',
      type: 'desktop',
      isAvailable: true,
      architecture: this.getSystemArchitecture(),
      osVersion: await this.getWindowsVersion(),
      deploymentType: 'desktop',
    });

    // Try to discover remote devices using WinAppDeployTool
    try {
      const remoteDevices = await this.discoverRemoteDevices();
      devices.push(...remoteDevices);
    } catch (error) {
      console.warn('Failed to discover remote Windows devices:', error);
    }

    // Try to discover Windows emulators
    try {
      const emulators = await this.discoverWindowsEmulators();
      devices.push(...emulators);
    } catch (error) {
      console.warn('Failed to discover Windows emulators:', error);
    }

    this.cachedDevices = devices;
    this.lastDeviceRefresh = now;
    return devices;
  }

  /**
   * Resolve the best device to use based on selection options
   */
  async resolveDeviceAsync(options?: DeviceSelectionOptions): Promise<WindowsDevice> {
    const devices = await this.getDevicesAsync();

    if (options?.deviceId) {
      const device = devices.find((d) => d.id === options.deviceId);
      if (!device) {
        throw new Error(
          `Windows device '${options.deviceId}' not found. Available devices: ${devices
            .map((d) => d.id)
            .join(', ')}`
        );
      }
      return device;
    }

    // Default to desktop for development
    const desktopDevice = devices.find((d) => d.type === 'desktop');
    if (!desktopDevice) {
      throw new Error('No Windows desktop device available');
    }

    return desktopDevice;
  }

  /**
   * Open a URL on the Windows device (required by DeviceManager base class)
   */
  async openUrlAsync(url: string, options?: { appId?: string }): Promise<void> {
    if (this.device.type === 'desktop') {
      // Use Windows shell to open URL on local desktop
      return new Promise((resolve, reject) => {
        const child = spawn('cmd', ['/c', 'start', url], {
          detached: true,
          stdio: 'ignore',
        });

        child.on('error', reject);
        child.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Failed to open URL on desktop: exit code ${code}`));
          }
        });

        child.unref();
      });
    } else {
      // For remote devices, we would use WinAppDeployTool
      // For now, throw an error indicating this needs implementation
      throw new Error(
        `Opening URLs on remote Windows devices is not yet implemented. Device: ${this.device.name}`
      );
    }
  }

  /**
   * Install an app on the Windows device (required by DeviceManager base class)
   */
  async installAppAsync(binaryPath: string): Promise<void> {
    if (this.device.type === 'desktop') {
      // For desktop, validate the app exists and is executable
      await this.validateDesktopApp(binaryPath);
    } else if (this.device.type === 'remote' && this.device._deviceInfo) {
      // Remote device deployment temporarily disabled due to WinAppDeployTool path issues
      throw new Error(
        'Remote device deployment temporarily disabled. WinAppDeployTool path configuration needs to be fixed.'
      );

      // TODO: Re-enable once WinAppDeployTool path issues are resolved
      // // Use react-native-windows WinAppDeployTool for remote device deployment
      // const deployTool = new WinAppDeployTool();
      //
      // if (!deployTool.isAvailable) {
      //   throw new Error('WinAppDeployTool not available for remote device deployment');
      // }
      //
      // // Install the app package using react-native-windows infrastructure
      // await deployTool.installAppPackage(
      //   binaryPath,
      //   this.device._deviceInfo,
      //   false, // shouldLaunch
      //   false, // shouldUpdate
      //   false, // pin
      //   false // verbose
      // );
    } else {
      throw new Error(
        `Installing apps on ${this.device.type} devices is not yet implemented. Device: ${this.device.name}`
      );
    }
  }

  /**
   * Check if an app is installed on the specified device
   */
  async isAppInstalledAsync(device: WindowsDevice, appId: string): Promise<boolean> {
    if (device.type === 'desktop') {
      // For desktop apps, check if executable exists
      const exePath = path.join(appId, 'App.exe');
      return fs.existsSync(exePath);
    } else {
      // For remote devices, we would query the device
      return false;
    }
  }

  /**
   * Launch an app on the specified device
   */
  async launchAppAsync(device: WindowsDevice, appId: string): Promise<void> {
    if (device.type === 'desktop') {
      await this.launchDesktopApp(appId);
    } else {
      throw new Error(
        `Launching apps on remote Windows devices is not yet implemented. Device: ${device.name}`
      );
    }
  }

  /**
   * Validate that a desktop app exists and is executable
   */
  private async validateDesktopApp(appPath: string): Promise<void> {
    if (!fs.existsSync(appPath)) {
      throw new Error(`Desktop app not found at path: ${appPath}`);
    }

    const stat = fs.statSync(appPath);

    if (stat.isDirectory()) {
      // If it's a directory, look for an executable
      const exeFiles = fs.readdirSync(appPath).filter((file) => file.endsWith('.exe'));
      if (exeFiles.length === 0) {
        throw new Error(`No executable found in directory: ${appPath}`);
      }
    } else if (stat.isFile()) {
      // If it's a file, check if it's executable
      if (!appPath.endsWith('.exe')) {
        throw new Error(`File is not an executable: ${appPath}`);
      }
    } else {
      throw new Error(`Invalid app path: ${appPath}`);
    }
  }

  /**
   * Launch a desktop application
   */
  private async launchDesktopApp(appPath: string): Promise<void> {
    await this.validateDesktopApp(appPath);

    let executablePath: string;

    if (fs.statSync(appPath).isDirectory()) {
      // Find the main executable in the directory
      const exeFiles = fs.readdirSync(appPath).filter((file) => file.endsWith('.exe'));
      executablePath = path.join(appPath, exeFiles[0]);
    } else {
      executablePath = appPath;
    }

    return new Promise((resolve, reject) => {
      const child = spawn(executablePath, [], {
        detached: true,
        stdio: 'ignore',
      });

      child.on('error', (error) => {
        reject(new Error(`Failed to launch desktop app: ${error.message}`));
      });

      child.on('spawn', () => {
        // App launched successfully
        child.unref(); // Allow the parent process to exit
        resolve();
      });
    });
  }

  /**
   * Get system architecture (x86, x64, ARM64)
   */
  private getSystemArchitecture(): 'x86' | 'x64' | 'ARM64' {
    const arch = process.arch;
    switch (arch) {
      case 'x64':
        return 'x64';
      case 'arm64':
        return 'ARM64';
      case 'ia32':
        return 'x86';
      default:
        return 'x64'; // Default fallback
    }
  }

  /**
   * Get Windows version information
   */
  private async getWindowsVersion(): Promise<string> {
    return new Promise((resolve) => {
      const child = spawn('cmd', ['/c', 'ver'], { stdio: 'pipe' });
      let output = '';

      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.on('close', () => {
        // Extract version from output like "Microsoft Windows [Version 10.0.19041.1234]"
        const match = output.match(/Version (\d+\.\d+\.\d+)/);
        resolve(match ? match[1] : '10.0.19041');
      });

      child.on('error', () => {
        resolve('10.0.19041'); // Default fallback
      });
    });
  }

  /**
   * Discover remote Windows devices using WinAppDeployTool
   */
  private async discoverRemoteDevices(): Promise<WindowsDevice[]> {
    const devices: WindowsDevice[] = [];

    try {
      // Check if WinAppDeployTool is available before trying to use it
      // WinAppDeployTool requires Windows SDK to be installed
      if (process.platform !== 'win32') {
        console.warn('WinAppDeployTool only available on Windows - skipping remote device discovery');
        return devices;
      }

      // Use react-native-windows WinAppDeployTool for device discovery
      // Note: WinAppDeployTool constructor may require specific parameters
      // For now, skip remote device discovery to avoid path errors
      console.warn('Remote device discovery temporarily disabled due to WinAppDeployTool path issues');
      return devices;

      // TODO: Re-enable once WinAppDeployTool path issues are resolved
      // const deployTool = new WinAppDeployTool();
      //
      // if (!deployTool.isAvailable) {
      //   console.warn('WinAppDeployTool not available - skipping remote device discovery');
      //   return devices;
      // }
      //
      // // Get devices using react-native-windows infrastructure
      // const deviceInfos = deployTool.enumerateDevices();
      //
      // for (const deviceInfo of deviceInfos) {
      //   devices.push({
      //     id: `remote-${deviceInfo.guid}`,
      //     name: `${deviceInfo.name} (${deviceInfo.ip})`,
      //     type: 'remote',
      //     isAvailable: true,
      //     architecture: 'x64', // Default assumption for remote devices
      //     osVersion: '10.0.19041', // Default Windows 10 version
      //     deploymentType: 'uwp',
      //     // Store additional info for deployment
      //     _deviceInfo: deviceInfo,
      //   });
      // }
    } catch (error) {
      console.warn('Failed to discover remote devices:', error);
    }

    return devices;
  }

  /**
   * Discover available Windows emulators
   */
  private async discoverWindowsEmulators(): Promise<WindowsDevice[]> {
    // TODO: Implement Windows Mobile emulator discovery
    // This would involve checking for installed Windows Mobile emulators
    // and HoloLens emulators through registry or Visual Studio APIs
    return [];
  }
}
