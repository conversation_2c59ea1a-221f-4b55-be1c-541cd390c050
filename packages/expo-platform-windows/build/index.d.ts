/**
 * Expo Platform Windows - External Platform Implementation
 *
 * This package provides complete Windows platform support for Expo applications
 * through the External Platform System. It implements all integration points
 * required for 100% feature parity with built-in iOS/Android platforms.
 */
import { ExternalPlatform } from '@expo/cli/src/core/PlatformRegistry';
/**
 * Main Windows platform definition that provides complete integration
 * with the Expo External Platform System.
 */
export declare const windowsPlatform: ExternalPlatform;
export default windowsPlatform;
export { WindowsDeviceManager } from './deviceManager/WindowsDeviceManager';
export { WindowsPlatformManager } from './platformManager/WindowsPlatformManager';
export { WindowsAutolinking } from './autolinking/WindowsAutolinking';
export * from './configPlugins';
export { withWindowsManifest, withWindowsAssets, withWindowsPermissions, withWindowsNewArch, } from './configPlugins';
export * from './assets';
export * from './devClient';
export * from './testing';
export * from './performance/optimizations';
export { runPerformanceOptimization } from './performance/runOptimization';
//# sourceMappingURL=index.d.ts.map