"use strict";
/**
 * Expo Platform Windows - External Platform Implementation
 *
 * This package provides complete Windows platform support for Expo applications
 * through the External Platform System. It implements all integration points
 * required for 100% feature parity with built-in iOS/Android platforms.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runPerformanceOptimization = exports.WindowsAutolinking = exports.WindowsPlatformManager = exports.WindowsDeviceManager = exports.windowsPlatform = void 0;
const WindowsAutolinking_1 = require("./autolinking/WindowsAutolinking");
const WindowsDeviceManager_1 = require("./deviceManager/WindowsDeviceManager");
const WindowsPlatformManager_1 = require("./platformManager/WindowsPlatformManager");
/**
 * Main Windows platform definition that provides complete integration
 * with the Expo External Platform System.
 */
exports.windowsPlatform = {
    platform: 'windows',
    displayName: 'Windows',
    // Development workflow integration (now properly implemented with base class extensions)
    resolveDeviceAsync: WindowsDeviceManager_1.WindowsDeviceManager.resolveAsync,
    platformManagerConstructor: WindowsPlatformManager_1.WindowsPlatformManager,
    // Build system integration
    configPlugins: [
        'withWindowsManifest',
        'withWindowsAssets',
        'withWindowsPermissions',
        'withWindowsNewArch',
    ],
    // Metro bundler integration
    metroExtensions: [
        '.windows.js',
        '.windows.ts',
        '.windows.tsx',
        '.windows.jsx',
        '.win32.js',
        '.win32.ts',
        '.win32.tsx',
        '.win32.jsx',
    ],
    // Native module autolinking
    autolinkingImplementation: new WindowsAutolinking_1.WindowsAutolinking(),
    // Prebuild template path (points to template root, not platform subdirectory)
    templatePath: './templates',
    // SDK module compatibility declarations
    supportedModules: {
        // Core modules with full Windows support
        'expo-constants': { supported: true },
        'expo-file-system': { supported: true },
        'expo-linking': { supported: true },
        'expo-web-browser': { supported: true },
        'expo-clipboard': { supported: true },
        'expo-crypto': { supported: true },
        'expo-device': { supported: true },
        'expo-font': { supported: true },
        'expo-keep-awake': { supported: true },
        'expo-linear-gradient': { supported: true },
        'expo-status-bar': { supported: true },
        'expo-mesh-gradient': { supported: true },
        // Modules with Windows-specific implementations
        'expo-camera': {
            supported: true,
            implementation: 'expo-camera/windows',
        },
        'expo-image': {
            supported: true,
            implementation: 'expo-image/windows',
        },
        'expo-av': {
            supported: true,
            implementation: 'expo-av/windows',
        },
        'expo-audio': {
            supported: true,
            implementation: 'expo-audio/windows',
        },
        'expo-video': {
            supported: true,
            implementation: 'expo-video/windows',
        },
        // Modules with graceful degradation
        'expo-notifications': {
            supported: false,
            fallback: 'graceful-degradation',
        },
        'expo-location': {
            supported: false,
            fallback: 'web-implementation',
        },
        'expo-sensors': {
            supported: false,
            fallback: 'graceful-degradation',
        },
    },
    // Asset management configuration
    assetHandlers: {
        appIcon: {
            sizes: [16, 24, 32, 48, 64, 128, 256],
            format: 'ico',
            generator: 'expo-platform-windows/assets/generateWindowsIcons',
        },
        splashScreen: {
            supported: true,
            handler: 'expo-platform-windows/assets/handleWindowsSplash',
        },
        fonts: {
            formats: ['ttf', 'otf', 'woff', 'woff2'],
            handler: 'expo-platform-windows/assets/handleWindowsFonts',
        },
    },
    // Windows permission system
    permissions: {
        camera: {
            key: 'webcam',
            description: 'Access camera for photo and video capture',
            required: false,
        },
        microphone: {
            key: 'microphone',
            description: 'Access microphone for audio recording',
            required: false,
        },
        location: {
            key: 'location',
            description: 'Access device location',
            required: false,
        },
        internetClient: {
            key: 'internetClient',
            description: 'Access internet for network requests',
            required: true,
        },
        picturesLibrary: {
            key: 'picturesLibrary',
            description: 'Access pictures library',
            required: false,
        },
        videosLibrary: {
            key: 'videosLibrary',
            description: 'Access videos library',
            required: false,
        },
        musicLibrary: {
            key: 'musicLibrary',
            description: 'Access music library',
            required: false,
        },
    },
};
// Export the platform as default for easy importing
exports.default = exports.windowsPlatform;
// Re-export all implementation classes for advanced usage
var WindowsDeviceManager_2 = require("./deviceManager/WindowsDeviceManager");
Object.defineProperty(exports, "WindowsDeviceManager", { enumerable: true, get: function () { return WindowsDeviceManager_2.WindowsDeviceManager; } });
var WindowsPlatformManager_2 = require("./platformManager/WindowsPlatformManager");
Object.defineProperty(exports, "WindowsPlatformManager", { enumerable: true, get: function () { return WindowsPlatformManager_2.WindowsPlatformManager; } });
var WindowsAutolinking_2 = require("./autolinking/WindowsAutolinking");
Object.defineProperty(exports, "WindowsAutolinking", { enumerable: true, get: function () { return WindowsAutolinking_2.WindowsAutolinking; } });
// Re-export config plugins
__exportStar(require("./configPlugins"), exports);
// Re-export asset handlers
__exportStar(require("./assets"), exports);
// Re-export development tools
__exportStar(require("./devClient"), exports);
// Note: Testing utilities are not exported from main package to avoid CLI conflicts
// Import directly from 'expo-platform-windows/testing' if needed
// Re-export performance optimization
__exportStar(require("./performance/optimizations"), exports);
var runOptimization_1 = require("./performance/runOptimization");
Object.defineProperty(exports, "runPerformanceOptimization", { enumerable: true, get: function () { return runOptimization_1.runPerformanceOptimization; } });
//# sourceMappingURL=index.js.map