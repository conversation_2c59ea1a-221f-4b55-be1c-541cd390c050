{"name": "expo-platform-windows", "version": "0.1.0-experimental", "description": "Experimental Windows platform support for Expo applications", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:modules": "node build/src/testing/runModuleValidation.js", "test:integration": "node build/src/testing/runIntegrationTests.js", "test:all": "npm run test && npm run test:modules && npm run test:integration", "optimize": "node build/src/performance/runOptimization.js", "validate": "node scripts/validate-implementation.js", "setup-demo": "./scripts/setup-demo.sh", "setup-demo-simple": "./scripts/setup-demo-simple.sh", "typecheck": "expo-module typecheck"}, "keywords": ["expo", "react-native", "windows", "platform", "external-platform"], "author": "Expo Team", "license": "MIT", "homepage": "https://github.com/expo/expo/tree/main/packages/expo-platform-windows", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-platform-windows"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "dependencies": {"@expo/cli": "*", "@expo/config-plugins": "*", "@expo/metro-config": "*", "@react-native-windows/cli": "^0.77.0", "fast-xml-parser": "^4.3.2", "glob": "^10.3.10", "react-native-windows": "^0.77.0", "semver": "^7.5.4", "sharp": "^0.33.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/jest": "^29.0.0", "@types/node": "^18.0.0", "@types/react": "^18.0.0", "@types/semver": "^7.5.6", "@types/xml2js": "^0.4.14", "expo-module-scripts": "*"}, "peerDependencies": {"react": "*", "react-native": "*"}, "expo": {"platforms": ["windows"]}, "expo-module": {"platforms": ["windows"]}}