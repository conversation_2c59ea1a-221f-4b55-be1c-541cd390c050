import { ExpoConfig } from '@expo/config';
import { ConfigPlugin } from '@expo/config-plugins';

import { PlatformDiscovery, platformRegistry } from '../core/PlatformRegistry';

const debug = require('debug')('expo:cli:external-platforms') as typeof console.log;

/**
 * Config plugin that automatically applies config plugins from registered external platforms.
 * This plugin should be applied early in the prebuild process to ensure external platform
 * config plugins are included in the configuration.
 */
export const withExternalPlatformPlugins: ConfigPlugin = (config) => {
  // Get all registered external platforms
  const externalPlatforms = platformRegistry.getAvailablePlatforms();

  if (externalPlatforms.length === 0) {
    debug('No external platforms registered, skipping config plugin application');
    return config;
  }

  debug(`Applying config plugins for external platforms: ${externalPlatforms.join(', ')}`);

  // Apply config plugins from each external platform
  for (const platform of externalPlatforms) {
    const platformData = platformRegistry.getPlatform(platform);
    if (platformData?.configPlugins) {
      debug(
        `Applying config plugins for ${platform}: ${platformData.configPlugins.length} plugins`
      );

      for (const pluginName of platformData.configPlugins) {
        try {
          let plugin;

          // Try to load plugin as a package-relative path first
          if (pluginName.startsWith('./') || pluginName.startsWith('../')) {
            // Relative path - resolve from platform package
            const platformPackageName = `expo-platform-${platform}`;
            try {
              const platformPackage = require(platformPackageName);
              const pluginPath = require.resolve(pluginName, { paths: [require.resolve(platformPackageName)] });
              plugin = require(pluginPath);
            } catch (relativeError: any) {
              debug(`Failed to load relative plugin ${pluginName}:`, relativeError.message);
              // Fall back to direct require
              plugin = require(pluginName);
            }
          } else {
            // Try direct require first (for absolute module names)
            try {
              plugin = require(pluginName);
            } catch (directError: any) {
              // If direct require fails, try loading from platform package
              const platformPackageName = `expo-platform-${platform}`;
              try {
                const platformPackage = require(platformPackageName);
                // Check if the plugin is exported from the platform package
                if (platformPackage[pluginName]) {
                  plugin = platformPackage[pluginName];
                } else if (platformPackage.default && platformPackage.default[pluginName]) {
                  plugin = platformPackage.default[pluginName];
                } else {
                  // Try loading from configPlugins export
                  const configPlugins = platformPackage.configPlugins || platformPackage.default?.configPlugins;
                  if (configPlugins && configPlugins[pluginName]) {
                    plugin = configPlugins[pluginName];
                  } else {
                    throw directError; // Re-throw original error if all attempts fail
                  }
                }
              } catch (packageError: any) {
                debug(`Failed to load plugin from platform package:`, packageError.message);
                throw directError; // Re-throw original error
              }
            }
          }

          // Apply the plugin
          if (typeof plugin === 'function') {
            config = plugin(config);
          } else if (plugin.default && typeof plugin.default === 'function') {
            config = plugin.default(config);
          } else {
            throw new Error(`Plugin ${pluginName} is not a function`);
          }

          debug(`Applied config plugin: ${pluginName}`);
        } catch (error: any) {
          console.warn(
            `Failed to apply config plugin ${pluginName} for platform ${platform}:`,
            error.message
          );
        }
      }
    }
  }

  return config;
};

/**
 * Loads external platforms and applies their config plugins to the Expo config.
 * This function should be called during the prebuild process to ensure external
 * platform config plugins are included.
 */
export async function withExternalPlatformsAsync(
  config: ExpoConfig,
  projectRoot: string
): Promise<ExpoConfig> {
  try {
    // Load external platforms first
    await PlatformDiscovery.loadExternalPlatforms(projectRoot);

    // Apply external platform config plugins
    return withExternalPlatformPlugins(config);
  } catch (error: any) {
    debug('Failed to load external platforms or apply config plugins:', error.message);
    // Return original config on error to ensure prebuild doesn't fail
    return config;
  }
}

/**
 * Applies external platform config plugins to an Expo config.
 * This is a shared utility that can be used by any CLI command that needs
 * to apply external platform config plugins.
 *
 * @param config The Expo config to modify
 * @param projectRoot The project root directory
 * @returns Promise<ExpoConfig> The modified config with external platform plugins applied
 * @deprecated Use withExternalPlatformsAsync instead - this function is identical and will be removed
 */
export async function applyExternalPlatformConfigPluginsAsync(
  config: ExpoConfig,
  projectRoot: string
): Promise<ExpoConfig> {
  // Delegate to the main function to avoid code duplication
  return withExternalPlatformsAsync(config, projectRoot);
}

/**
 * Log information about loaded external platforms.
 * This is useful for debugging and understanding what platforms are available.
 */
export function logExternalPlatformInfo(): void {
  const externalPlatforms = platformRegistry.getAvailablePlatforms();

  if (externalPlatforms.length === 0) {
    debug('No external platforms loaded');
    return;
  }

  console.log(`📱 External platforms loaded: ${externalPlatforms.join(', ')}`);

  for (const platform of externalPlatforms) {
    const platformData = platformRegistry.getPlatform(platform);
    if (platformData) {
      const features = [];
      if (platformData.configPlugins?.length)
        features.push(`${platformData.configPlugins.length} config plugins`);
      if (platformData.metroExtensions?.length)
        features.push(`${platformData.metroExtensions.length} Metro extensions`);
      if (platformData.templatePath) features.push('template');

      if (features.length > 0) {
        console.log(`   ${platform}: ${features.join(', ')}`);
      }
    }
  }
}
